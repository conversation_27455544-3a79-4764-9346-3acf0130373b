[{"command": "runsim -block ap_sys -case page_test_027 -rundir page_test_027_test -fsdb", "timestamp": "2025-07-22 16:27:14"}, {"command": "runsim -block ap_sys -case page_test_027 -R", "timestamp": "2025-07-21 19:10:18"}, {"command": "runsim -block apcpu_sys -case apcpu_sys_passive_test", "timestamp": "2025-07-21 19:07:41"}, {"command": "runsim -block apcpu_sys -case apcpu_sys_stress_test -fsdb -R", "timestamp": "2025-07-21 19:07:32"}, {"command": "runsim -base top -block udtb/usvp -case apcpu_memset_test_top -fsdb", "timestamp": "2025-07-21 19:07:02"}, {"command": "runsim -base apcpu_sys -block udtb/apcpu_sys/dvfs -case top_dvfs_temp_basic_test -fsdb", "timestamp": "2025-07-21 19:03:36"}, {"command": "runsim -base apcpu_sys -block udtb/apcpu_sys/dvfs -case top_dvfs_temp_basic_test", "timestamp": "2025-07-21 18:55:14"}, {"command": "runsim -base top -block udtb/usvp -case apcpu_memset_test_top", "timestamp": "2025-07-21 14:37:48"}, {"command": "BATCH RUN 多次执行: page_test_023 (3 次)", "timestamp": "2025-07-17 18:44:36"}, {"command": "BATCH RUN 多次执行: ap_sys_stress_test (3 次)", "timestamp": "2025-07-17 18:29:06"}, {"command": "BATCH RUN 多次执行: apcpu_clk_scan_test (3 次)", "timestamp": "2025-07-17 14:05:14"}, {"command": "BATCH RUN 多次执行: apcpu_stress_test (3 次)", "timestamp": "2025-07-17 13:58:07"}, {"command": "BATCH RUN 多次执行: apcpu_sys_stress_test (4 次)", "timestamp": "2025-07-16 10:56:07"}, {"command": "BATCH RUN 多次执行: apcpu_hello_world (3 次)", "timestamp": "2025-07-16 10:33:39"}, {"command": "runsim -base apcpu_sys -block udtb/apcpu_sys/apcpu_sys_clk -case apcpu_clk_sel_test", "timestamp": "2025-07-15 18:49:06"}, {"command": "BATCH RUN 多次执行: apcpu_hello_world (5 次)", "timestamp": "2025-07-15 18:24:40"}, {"command": "runsim -base apcpu_sys -block udtb/usvp -case apcpu_hello_world -R E:\\doc\\python\\runsim\\apcpu_base_case -seed 1797825344 -rundir apcpu_hello_world_1797825344 & runsim -base apcpu_sys -block udtb/usvp -case apcpu_hello_world -R E:\\doc\\python\\runsim\\apcpu_base_case -seed 325109473 -rundir apcpu_hello_world_325109473 & runsim -base apcpu_sys -block udtb/usvp -case apcpu_hello_world -R E:\\doc\\python\\runsim\\apcpu_base_case -seed 1668160675 -rundir apcpu_hello_world_1668160675 & runsim -base apcpu_sys -block udtb/usvp -case apcpu_hello_world -R E:\\doc\\python\\runsim\\apcpu_base_case -seed 1161427432 -rundir apcpu_hello_world_1161427432 & runsim -base apcpu_sys -block udtb/usvp -case apcpu_hello_world -R E:\\doc\\python\\runsim\\apcpu_base_case -seed 340709616 -rundir apcpu_hello_world_340709616 & wait", "timestamp": "2025-07-15 18:12:17"}, {"command": "runsim -base apcpu_sys -block udtb/usvp -case apcpu_base_case", "timestamp": "2025-07-11 09:47:38"}, {"command": "runsim -base apcpu_sys -block udtb/usvp -case apcpu_base_case -C ; runsim -base apcpu_sys -block udtb/usvp -case apcpu_hello_world -R E:\\doc\\python\\runsim\\apcpu_base_case", "timestamp": "2025-07-10 13:48:16"}, {"command": "runsim -block apcpu_sys -case apcpu_sys_stress_test -rundir apcpu_sys_stress_test_try", "timestamp": "2025-07-09 17:29:42"}, {"command": "runsim -base apcpu_sys -block udtb/usvp -case apcpu_hello_world", "timestamp": "2025-07-09 17:28:28"}, {"command": "runsim -base apcpu_sys -block udtb/usvp -case apcpu_base_case -R -C ; runsim -base apcpu_sys -block udtb/usvp -case apcpu_hello_world -R", "timestamp": "2025-07-08 18:09:37"}, {"command": "runsim -base apcpu_sys -block udtb/usvp -case apcpu_hello_world -R E:\\doc\\python\\runsim\\apcpu_base_case", "timestamp": "2025-07-08 17:46:47"}, {"command": "runsim -base apcpu_sys -block udtb/usvp -case apcpu_base_case -C ; runsim -base apcpu_sys -block udtb/usvp -case apcpu_stress_test -R E:\\doc\\python\\runsim\\apcpu_base_case", "timestamp": "2025-07-08 17:41:11"}, {"command": "使用基础用例运行仿真: runsim -base apcpu_sys -block udtb/usvp -case apcpu_hello_world -R E:\\doc\\python\\runsim\\apcpu_base_case", "timestamp": "2025-07-08 17:29:10"}, {"command": "编译基础用例: apcpu_base_case: runsim -base apcpu_sys -block udtb/usvp -case apcpu_base_case -C", "timestamp": "2025-07-08 17:07:51"}, {"command": "使用基础用例运行仿真: runsim -base apcpu_sys -block udtb/usvp -case apcpu_stress_test -R E:\\doc\\python\\runsim\\apcpu_base_case", "timestamp": "2025-07-08 17:07:51"}, {"command": "使用基础用例运行仿真: runsim -base top -block udtb/usvp -case apcpu_stress_test_top -R E:\\doc\\python\\runsim\\apcpu_base_case_top", "timestamp": "2025-07-08 17:04:00"}, {"command": "编译基础用例: apcpu_base_case_top: runsim -base top -block udtb/usvp -case apcpu_base_case_top -C", "timestamp": "2025-07-08 17:04:00"}, {"command": "runsim -base top -block udtb/top/top_clk -case apcpu_sys_bus_mini_test", "timestamp": "2025-07-08 13:32:58"}, {"command": "runsim -base top -block udtb/top/top_clk -case apcpu_sys_passive_test", "timestamp": "2025-07-08 13:32:58"}, {"command": "runsim -base top -block udtb/top/top_clk -case apcpu_sys_stress_test", "timestamp": "2025-07-08 13:32:58"}, {"command": "runsim -base top -block udtb/top/top_clk -case apcpu_hello_world_top", "timestamp": "2025-07-07 16:40:38"}, {"command": "runsim -base top -block udtb/top/top_clk -case apcpu_stress_test_top", "timestamp": "2025-07-07 16:40:38"}, {"command": "runsim -base top -block udtb/top/top_clk -case apcpu_memset_test_top", "timestamp": "2025-07-07 16:40:36"}, {"command": "runsim -block top -case top_bus_mini_test", "timestamp": "2025-07-07 15:54:37"}, {"command": "runsim -block top -case top_passive_test", "timestamp": "2025-07-07 15:54:35"}, {"command": "runsim -block top -case top_stress_test", "timestamp": "2025-07-07 15:54:33"}, {"command": "runsim -base top -block udtb/top/top_clk -case apcpu_clk_scan_test", "timestamp": "2025-07-04 10:19:53"}, {"command": "runsim -base top -block udtb/top/top_clk -case apcpu_clk_sel_test", "timestamp": "2025-07-04 10:19:53"}, {"command": "runsim -base top -block udtb/top/top_clk -case top_bus_mini_test", "timestamp": "2025-07-04 10:19:53"}, {"command": "runsim -base top -block udtb/top/top_clk -case top_passive_test", "timestamp": "2025-07-04 10:19:53"}, {"command": "runsim -base top -block udtb/top/top_clk -case top_stress_test", "timestamp": "2025-07-04 10:19:52"}, {"command": "runsim -base top -block udtb/top/top_clk -case apcpu_clk_bist_test", "timestamp": "2025-07-04 10:19:31"}, {"command": "runsim -base top -block udtb/top/top_clk -case apcpu_clk_bist_test -fsdb", "timestamp": "2025-07-04 10:13:35"}, {"command": "runsim -base top -block udtb/top/top_clk -case apcpu_clk_scan_test -fsdb", "timestamp": "2025-07-04 10:13:35"}, {"command": "runsim -base top -block udtb/top/top_clk -case apcpu_clk_sel_test -fsdb", "timestamp": "2025-07-04 10:13:35"}, {"command": "runsim -base top -block udtb/top/top_clk -case top_bus_mini_test -fsdb", "timestamp": "2025-07-04 10:13:34"}, {"command": "runsim -base top -block udtb/top/top_clk -case top_passive_test -fsdb", "timestamp": "2025-07-04 10:13:33"}, {"command": "runsim -base top -block udtb/top/top_clk -case top_stress_test -fsdb", "timestamp": "2025-07-04 10:13:33"}]