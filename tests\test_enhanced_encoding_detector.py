"""
增强编码检测器单元测试
测试各种编码格式和特殊字符处理功能
"""
import unittest
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.enhanced_encoding_detector import (
    EnhancedEncodingDetector, 
    LinePatternType, 
    DecodingResult,
    SpecialCharInfo
)


class TestEnhancedEncodingDetector(unittest.TestCase):
    """增强编码检测器测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.detector = EnhancedEncodingDetector(
            cache_size=100,
            enable_chardet=True,
            enable_pattern_detection=True
        )
    
    def tearDown(self):
        """测试后清理"""
        self.detector.cleanup()
    
    def test_basic_utf8_detection(self):
        """测试基本UTF-8编码检测"""
        test_text = "Hello, 世界! 这是一个测试。"
        raw_bytes = test_text.encode('utf-8')
        
        result = self.detector.decode_with_fallback(raw_bytes)
        
        self.assertEqual(result.text, test_text)
        self.assertEqual(result.encoding, 'utf-8')
        self.assertGreater(result.confidence, 0.8)
        self.assertTrue(result.has_special_chars)  # 包含Unicode字符
    
    def test_gbk_encoding_detection(self):
        """测试GBK编码检测"""
        if sys.platform != 'win32':
            self.skipTest("GBK测试仅在Windows平台运行")
        
        test_text = "你好，世界！这是GBK编码测试。"
        try:
            raw_bytes = test_text.encode('gbk')
            result = self.detector.decode_with_fallback(raw_bytes)
            
            self.assertEqual(result.text, test_text)
            self.assertIn(result.encoding, ['gbk', 'cp936'])
            self.assertGreater(result.confidence, 0.6)
        except UnicodeEncodeError:
            self.skipTest("当前系统不支持GBK编码")
    
    def test_ascii_detection(self):
        """测试ASCII编码检测"""
        test_text = "Hello, World! This is ASCII text."
        raw_bytes = test_text.encode('ascii')
        
        result = self.detector.decode_with_fallback(raw_bytes)
        
        self.assertEqual(result.text, test_text)
        self.assertIn(result.encoding, ['ascii', 'utf-8'])
        self.assertGreater(result.confidence, 0.9)
        self.assertFalse(result.has_special_chars)
    
    def test_latin1_detection(self):
        """测试Latin-1编码检测"""
        test_text = "Café, naïve, résumé"
        raw_bytes = test_text.encode('latin1')
        
        result = self.detector.decode_with_fallback(raw_bytes)
        
        # 可能被检测为latin1、utf-8或cp1252
        self.assertIsNotNone(result.text)
        self.assertIn(result.encoding, ['latin1', 'utf-8', 'cp1252'])
        self.assertGreater(result.confidence, 0.5)
    
    def test_bom_detection(self):
        """测试BOM检测"""
        test_text = "UTF-8 with BOM"
        
        # UTF-8 BOM
        raw_bytes = b'\xef\xbb\xbf' + test_text.encode('utf-8')
        result = self.detector.decode_with_fallback(raw_bytes)
        self.assertEqual(result.encoding, 'utf-8-sig')
        
        # UTF-16 LE BOM
        raw_bytes = b'\xff\xfe' + test_text.encode('utf-16le')
        result = self.detector.decode_with_fallback(raw_bytes)
        self.assertEqual(result.encoding, 'utf-16-le')
        
        # UTF-16 BE BOM
        raw_bytes = b'\xfe\xff' + test_text.encode('utf-16be')
        result = self.detector.decode_with_fallback(raw_bytes)
        self.assertEqual(result.encoding, 'utf-16-be')
    
    def test_ansi_escape_sequences(self):
        """测试ANSI转义序列处理"""
        test_text = "\x1b[31mRed text\x1b[0m \x1b[32mGreen text\x1b[0m"
        raw_bytes = test_text.encode('utf-8')
        
        result = self.detector.decode_with_fallback(raw_bytes)
        
        self.assertIn("Red text", result.text)
        self.assertIn("Green text", result.text)
        self.assertTrue(result.has_special_chars)
        
        # 检查是否检测到ANSI颜色模式
        ansi_patterns = [p for p in result.line_patterns if p.pattern_type == LinePatternType.ANSI_COLORED]
        self.assertGreater(len(ansi_patterns), 0)
    
    def test_tab_character_handling(self):
        """测试制表符处理"""
        test_text = "Column1\tColumn2\tColumn3\n"
        raw_bytes = test_text.encode('utf-8')
        
        result = self.detector.decode_with_fallback(raw_bytes)
        
        # 制表符应该被转换为4个空格
        self.assertNotIn('\t', result.text)
        self.assertIn('    ', result.text)
        self.assertTrue(result.has_special_chars)
    
    def test_unicode_character_handling(self):
        """测试Unicode字符处理"""
        test_text = "Unicode: 🌟 ★ ♠ ♥ ♦ ♣ 中文 한글 العربية"
        raw_bytes = test_text.encode('utf-8')
        
        result = self.detector.decode_with_fallback(raw_bytes)
        
        self.assertEqual(result.text, test_text.replace('\t', '    '))
        self.assertTrue(result.has_special_chars)
        
        # 检查是否检测到Unicode重度使用模式
        unicode_patterns = [p for p in result.line_patterns if p.pattern_type == LinePatternType.UNICODE_HEAVY]
        self.assertGreater(len(unicode_patterns), 0)
    
    def test_table_pattern_detection(self):
        """测试表格模式检测"""
        table_text = """
|Name    |Age |City     |
|--------|----|---------| 
|Alice   |25  |New York |
|Bob     |30  |London   |
"""
        raw_bytes = table_text.encode('utf-8')
        
        result = self.detector.decode_with_fallback(raw_bytes)
        
        # 检查是否检测到表格模式
        table_patterns = [p for p in result.line_patterns 
                         if p.pattern_type in [LinePatternType.TABLE_HEADER, LinePatternType.TABLE_ROW]]
        self.assertGreater(len(table_patterns), 0)
    
    def test_code_block_detection(self):
        """测试代码块检测"""
        code_text = """
def hello_world():
    print("Hello, World!")
    if True:
        print("Indented code")
"""
        raw_bytes = code_text.encode('utf-8')
        
        result = self.detector.decode_with_fallback(raw_bytes)
        
        # 检查是否检测到代码块模式
        code_patterns = [p for p in result.line_patterns if p.pattern_type == LinePatternType.CODE_BLOCK]
        self.assertGreater(len(code_patterns), 0)
    
    def test_multiline_patterns(self):
        """测试多行模式检测"""
        multiline_text = """
Normal text line
    Indented code block
        More indented
  Slightly indented text
|Table |Header|
|------|------|
|Cell1 |Cell2 |
\x1b[31mColored text\x1b[0m
Unicode heavy: 中文测试文本包含大量Unicode字符
"""
        raw_bytes = multiline_text.encode('utf-8')
        
        patterns = self.detector.detect_multiline_patterns(raw_bytes)
        
        # 应该检测到多种模式
        pattern_types = {p.pattern_type for p in patterns}
        expected_types = {
            LinePatternType.CODE_BLOCK,
            LinePatternType.INDENTED_TEXT,
            LinePatternType.TABLE_ROW,
            LinePatternType.ANSI_COLORED,
            LinePatternType.UNICODE_HEAVY
        }
        
        # 至少应该检测到其中几种模式
        self.assertGreater(len(pattern_types.intersection(expected_types)), 2)
    
    def test_encoding_confidence(self):
        """测试编码置信度评估"""
        # 高置信度UTF-8
        utf8_text = "这是UTF-8编码的中文文本"
        utf8_bytes = utf8_text.encode('utf-8')
        confidence = self.detector.get_encoding_confidence(utf8_bytes)
        self.assertGreater(confidence, 0.8)
        
        # 低置信度（混乱的字节）
        random_bytes = bytes([0x80, 0x81, 0x82, 0x83, 0x84])
        confidence = self.detector.get_encoding_confidence(random_bytes)
        self.assertLess(confidence, 0.5)
    
    def test_special_character_analysis(self):
        """测试特殊字符分析"""
        test_text = "\x1b[31mColored\x1b[0m\tTabbed\n中文Unicode"
        raw_bytes = test_text.encode('utf-8')
        
        result = self.detector.decode_with_fallback(raw_bytes)
        
        self.assertTrue(result.has_special_chars)
        
        # 通过内部方法测试特殊字符分析
        special_info = self.detector._analyze_special_characters(test_text)
        self.assertTrue(special_info.has_ansi_sequences)
        self.assertTrue(special_info.has_tabs)
        self.assertTrue(special_info.has_unicode)
        self.assertGreater(special_info.ansi_color_count, 0)
        self.assertGreater(special_info.tab_count, 0)
        self.assertGreater(special_info.unicode_char_count, 0)
    
    def test_caching_mechanism(self):
        """测试缓存机制"""
        test_text = "Cached text for testing"
        raw_bytes = test_text.encode('utf-8')
        
        # 第一次调用
        result1 = self.detector.decode_with_fallback(raw_bytes)
        stats1 = self.detector.get_stats()
        
        # 第二次调用（应该使用缓存）
        result2 = self.detector.decode_with_fallback(raw_bytes)
        stats2 = self.detector.get_stats()
        
        # 结果应该相同
        self.assertEqual(result1.text, result2.text)
        self.assertEqual(result1.encoding, result2.encoding)
        
        # 缓存命中率应该增加
        self.assertGreater(stats2['cache_hits'], stats1['cache_hits'])
    
    def test_error_recovery(self):
        """测试错误恢复机制"""
        # 无效的字节序列
        invalid_bytes = b'\xff\xfe\x00\x01\x02\x03'
        
        result = self.detector.decode_with_fallback(invalid_bytes)
        
        # 应该能够返回结果（使用替换模式）
        self.assertIsNotNone(result.text)
        self.assertIsNotNone(result.encoding)
        self.assertGreater(result.confidence, 0.0)
    
    def test_empty_input(self):
        """测试空输入"""
        result = self.detector.decode_with_fallback(b'')
        
        self.assertEqual(result.text, "")
        self.assertEqual(result.encoding, "utf-8")
        self.assertEqual(result.confidence, 1.0)
        self.assertFalse(result.has_special_chars)
        self.assertEqual(len(result.line_patterns), 0)
    
    def test_hint_encoding(self):
        """测试编码提示功能"""
        test_text = "Test with encoding hint"
        raw_bytes = test_text.encode('utf-8')
        
        result = self.detector.decode_with_fallback(raw_bytes, hint_encoding='utf-8')
        
        self.assertEqual(result.text, test_text)
        self.assertEqual(result.encoding, 'utf-8')
        self.assertGreater(result.confidence, 0.8)
    
    def test_performance_stats(self):
        """测试性能统计"""
        test_texts = [
            "Text 1",
            "Text 2 with 中文",
            "\x1b[31mColored text\x1b[0m",
            "Table\tData\tHere"
        ]
        
        for text in test_texts:
            raw_bytes = text.encode('utf-8')
            self.detector.decode_with_fallback(raw_bytes)
        
        stats = self.detector.get_stats()
        
        self.assertGreater(stats['total_detections'], 0)
        self.assertIsNotNone(stats['most_common_encoding'])
        self.assertGreaterEqual(stats['cache_hit_rate'], 0)
        self.assertIn('encoding_distribution', stats)
        self.assertIn('pattern_distribution', stats)
        self.assertIn('special_char_stats', stats)
    
    def test_cleanup(self):
        """测试资源清理"""
        # 添加一些数据
        test_text = "Test cleanup"
        raw_bytes = test_text.encode('utf-8')
        self.detector.decode_with_fallback(raw_bytes)
        
        # 清理前应该有数据
        stats_before = self.detector.get_stats()
        self.assertGreater(stats_before['total_detections'], 0)
        
        # 清理
        self.detector.cleanup()
        
        # 清理后统计应该重置
        stats_after = self.detector.get_stats()
        self.assertEqual(stats_after['total_detections'], 0)
        self.assertEqual(stats_after['cache_size'], 0)


class TestEncodingDetectorIntegration(unittest.TestCase):
    """编码检测器集成测试"""
    
    def setUp(self):
        self.detector = EnhancedEncodingDetector()
    
    def tearDown(self):
        self.detector.cleanup()
    
    def test_mixed_encoding_content(self):
        """测试混合编码内容处理"""
        # 模拟可能出现的混合内容场景
        contents = [
            "ASCII content",
            "UTF-8 中文内容",
            "\x1b[32mANSI colored text\x1b[0m",
            "Tab\tseparated\tvalues",
            "Unicode symbols: ★ ♠ ♥ ♦"
        ]
        
        for content in contents:
            raw_bytes = content.encode('utf-8')
            result = self.detector.decode_with_fallback(raw_bytes)
            
            self.assertIsNotNone(result.text)
            self.assertGreater(result.confidence, 0.5)
            self.assertIsNotNone(result.encoding)
    
    def test_large_content_performance(self):
        """测试大内容性能"""
        # 生成较大的测试内容
        large_content = ["Line {}: This is a test line with some content.\n".format(i) for i in range(1000)]
        large_text = "".join(large_content)
        raw_bytes = large_text.encode('utf-8')
        
        import time
        start_time = time.time()
        result = self.detector.decode_with_fallback(raw_bytes)
        end_time = time.time()
        
        # 处理时间应该在合理范围内（小于1秒）
        processing_time = end_time - start_time
        self.assertLess(processing_time, 1.0)
        
        # 结果应该正确
        self.assertIsNotNone(result.text)
        self.assertEqual(result.encoding, 'utf-8')


if __name__ == '__main__':
    # 创建测试目录（如果不存在）
    test_dir = os.path.dirname(os.path.abspath(__file__))
    if not os.path.exists(test_dir):
        os.makedirs(test_dir)
    
    # 运行测试
    unittest.main(verbosity=2)