"""
增强编码检测器 - 提供更强大的编码检测和文本处理能力
实现多重编码检测策略、特殊字符处理、多行模式识别等功能
"""
import sys
import re
import time
from dataclasses import dataclass
from typing import List, Optional, Dict, Any, Tuple
from collections import OrderedDict
from enum import Enum

# chardet是可选依赖
try:
    import chardet
    CHARDET_AVAILABLE = True
except ImportError:
    CHARDET_AVAILABLE = False


class LinePatternType(Enum):
    """行模式类型"""
    NORMAL = "normal"
    TABLE_HEADER = "table_header"
    TABLE_ROW = "table_row"
    CODE_BLOCK = "code_block"
    INDENTED_TEXT = "indented_text"
    ANSI_COLORED = "ansi_colored"
    UNICODE_HEAVY = "unicode_heavy"


@dataclass
class LinePattern:
    """行模式信息"""
    pattern_type: LinePatternType
    confidence: float
    metadata: Dict[str, Any]


@dataclass
class DecodingResult:
    """解码结果"""
    text: str
    encoding: str
    confidence: float
    has_special_chars: bool
    line_patterns: List[LinePattern]
    processing_time_ms: float


@dataclass
class SpecialCharInfo:
    """特殊字符信息"""
    has_ansi_sequences: bool
    has_tabs: bool
    has_unicode: bool
    has_control_chars: bool
    ansi_color_count: int
    tab_count: int
    unicode_char_count: int


class EnhancedEncodingDetector:
    """
    增强编码检测器
    
    功能特性：
    1. 多重编码检测策略（快速检测 + chardet + 模式匹配）
    2. 特殊字符处理（ANSI转义序列、制表符、Unicode字符）
    3. 多行模式识别（表格、代码块等特殊格式）
    4. 编码置信度评估
    5. 性能优化和缓存机制
    """
    
    def __init__(self, cache_size=2000, enable_chardet=True, enable_pattern_detection=True):
        """
        初始化增强编码检测器
        
        Args:
            cache_size (int): 编码缓存大小
            enable_chardet (bool): 是否启用chardet库
            enable_pattern_detection (bool): 是否启用模式检测
        """
        self.cache_size = cache_size
        self.enable_chardet = enable_chardet and CHARDET_AVAILABLE
        self.enable_pattern_detection = enable_pattern_detection
        
        # 编码缓存（LRU）
        self._encoding_cache = OrderedDict()
        self._pattern_cache = OrderedDict()
        
        # 平台特定的编码优先级
        if sys.platform == 'win32':
            self._primary_encodings = ['utf-8', 'gbk', 'cp936', 'cp1252', 'latin1']
        else:
            self._primary_encodings = ['utf-8', 'latin1', 'ascii', 'iso-8859-1']
        
        self._fallback_encoding = 'utf-8'
        
        # ANSI转义序列正则表达式
        self._ansi_escape_pattern = re.compile(r'\x1b\[[0-9;]*[mK]')
        self._ansi_color_pattern = re.compile(r'\x1b\[[0-9;]*m')
        
        # 表格模式检测正则表达式
        self._table_patterns = [
            re.compile(r'^[\s]*\|.*\|[\s]*$'),  # |列1|列2|列3|
            re.compile(r'^[\s]*\+[-=]+\+.*\+[\s]*$'),  # +----+----+
            re.compile(r'^[\s]*[-=]{3,}[\s]*$'),  # --------
            re.compile(r'^[\s]*[^\s]+\s+[^\s]+\s+[^\s]+'),  # 空格分隔的多列
        ]
        
        # Unicode字符范围检测
        self._unicode_ranges = [
            (0x4E00, 0x9FFF),  # CJK统一汉字
            (0x3400, 0x4DBF),  # CJK扩展A
            (0x20000, 0x2A6DF),  # CJK扩展B
            (0xAC00, 0xD7AF),  # 韩文音节
            (0x0400, 0x04FF),  # 西里尔字母
            (0x0370, 0x03FF),  # 希腊字母
        ]
        
        # 统计信息
        self._stats = {
            'total_detections': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'chardet_calls': 0,
            'pattern_detections': 0,
            'encoding_distribution': {},
            'pattern_distribution': {},
            'avg_detection_time': 0.0,
            'special_char_stats': {
                'ansi_sequences': 0,
                'tabs': 0,
                'unicode_chars': 0,
                'control_chars': 0
            }
        }
        
        # 性能优化设置
        self._min_bytes_for_detection = 16
        self._max_bytes_for_detection = 2048
        self._pattern_detection_max_lines = 50
    
    def decode_with_fallback(self, raw_bytes: bytes, hint_encoding: str = None) -> DecodingResult:
        """
        使用回退机制解码字节数据
        
        Args:
            raw_bytes: 原始字节数据
            hint_encoding: 编码提示
            
        Returns:
            DecodingResult: 解码结果
        """
        if not raw_bytes:
            return DecodingResult(
                text="",
                encoding="utf-8",
                confidence=1.0,
                has_special_chars=False,
                line_patterns=[],
                processing_time_ms=0.0
            )
        
        start_time = time.time()
        self._stats['total_detections'] += 1
        
        try:
            # 生成缓存键
            cache_key = self._generate_cache_key(raw_bytes)
            
            # 尝试使用缓存
            cached_result = self._get_cached_result(cache_key)
            if cached_result:
                processing_time = (time.time() - start_time) * 1000
                cached_result.processing_time_ms = processing_time
                return cached_result
            
            # 多重编码检测
            encoding, confidence = self._detect_encoding_with_confidence(raw_bytes, hint_encoding)
            
            # 解码文本
            text = self._decode_with_encoding(raw_bytes, encoding)
            
            # 处理特殊字符
            processed_text = self.handle_special_characters(text)
            
            # 检测特殊字符信息
            special_char_info = self._analyze_special_characters(text)
            
            # 检测行模式
            line_patterns = []
            if self.enable_pattern_detection:
                line_patterns = self.detect_multiline_patterns(raw_bytes)
            
            # 创建结果
            result = DecodingResult(
                text=processed_text,
                encoding=encoding,
                confidence=confidence,
                has_special_chars=special_char_info.has_ansi_sequences or 
                                special_char_info.has_tabs or 
                                special_char_info.has_unicode,
                line_patterns=line_patterns,
                processing_time_ms=(time.time() - start_time) * 1000
            )
            
            # 缓存结果
            self._cache_result(cache_key, result)
            
            # 更新统计
            self._update_stats(encoding, special_char_info, line_patterns)
            
            return result
            
        except Exception as e:
            print(f"增强编码检测出错: {str(e)}")
            # 使用替换模式的UTF-8作为最后手段
            fallback_text = raw_bytes.decode(self._fallback_encoding, errors='replace')
            return DecodingResult(
                text=fallback_text,
                encoding=self._fallback_encoding,
                confidence=0.1,
                has_special_chars=False,
                line_patterns=[],
                processing_time_ms=(time.time() - start_time) * 1000
            )
    
    def detect_multiline_patterns(self, raw_bytes: bytes) -> List[LinePattern]:
        """
        检测多行模式
        
        Args:
            raw_bytes: 原始字节数据
            
        Returns:
            List[LinePattern]: 检测到的行模式列表
        """
        if not self.enable_pattern_detection:
            return []
        
        try:
            # 先用UTF-8尝试解码进行模式检测
            try:
                text = raw_bytes.decode('utf-8', errors='ignore')
            except:
                text = raw_bytes.decode(self._fallback_encoding, errors='replace')
            
            lines = text.split('\n')[:self._pattern_detection_max_lines]
            patterns = []
            
            for i, line in enumerate(lines):
                if not line.strip():
                    continue
                
                pattern = self._detect_line_pattern(line, i, lines)
                if pattern:
                    patterns.append(pattern)
            
            self._stats['pattern_detections'] += len(patterns)
            return patterns
            
        except Exception as e:
            print(f"多行模式检测出错: {str(e)}")
            return []
    
    def handle_special_characters(self, text: str) -> str:
        """
        处理特殊字符
        
        Args:
            text: 输入文本
            
        Returns:
            str: 处理后的文本
        """
        if not text:
            return text
        
        processed_text = text
        
        try:
            # 处理制表符 - 转换为4个空格
            processed_text = processed_text.replace('\t', '    ')
            
            # 处理回车符
            processed_text = processed_text.replace('\r\n', '\n').replace('\r', '\n')
            
            # 处理其他控制字符（保留ANSI转义序列）
            processed_text = self._clean_control_characters(processed_text)
            
            # 处理Unicode字符 - 确保正确显示
            processed_text = self._normalize_unicode(processed_text)
            
            return processed_text
            
        except Exception as e:
            print(f"特殊字符处理出错: {str(e)}")
            return text
    
    def get_encoding_confidence(self, raw_bytes: bytes) -> float:
        """
        获取编码置信度
        
        Args:
            raw_bytes: 原始字节数据
            
        Returns:
            float: 置信度 (0.0-1.0)
        """
        if not raw_bytes:
            return 1.0
        
        try:
            encoding, confidence = self._detect_encoding_with_confidence(raw_bytes)
            return confidence
        except:
            return 0.1
    
    def _detect_encoding_with_confidence(self, raw_bytes: bytes, hint_encoding: str = None) -> Tuple[str, float]:
        """
        检测编码并返回置信度
        
        Args:
            raw_bytes: 原始字节数据
            hint_encoding: 编码提示
            
        Returns:
            Tuple[str, float]: (编码名称, 置信度)
        """
        if len(raw_bytes) < self._min_bytes_for_detection:
            return hint_encoding or self._primary_encodings[0], 0.8
        
        detection_bytes = raw_bytes[:self._max_bytes_for_detection]
        best_encoding = None
        best_confidence = 0.0
        
        # 1. 快速检测（高置信度）
        quick_encoding = self._quick_encoding_detection(detection_bytes)
        if quick_encoding:
            if self._test_encoding(detection_bytes, quick_encoding):
                return quick_encoding, 0.95
        
        # 2. 提示编码检测
        if hint_encoding and self._test_encoding(detection_bytes, hint_encoding):
            return hint_encoding, 0.9
        
        # 3. 主要编码检测
        for encoding in self._primary_encodings:
            if self._test_encoding(detection_bytes, encoding):
                confidence = self._calculate_encoding_confidence(detection_bytes, encoding)
                if confidence > best_confidence:
                    best_encoding = encoding
                    best_confidence = confidence
        
        # 4. chardet检测
        if self.enable_chardet and CHARDET_AVAILABLE and len(detection_bytes) >= 32:
            chardet_result = self._chardet_detection_with_confidence(detection_bytes)
            if chardet_result and chardet_result[1] > best_confidence:
                best_encoding, best_confidence = chardet_result
        
        return best_encoding or self._fallback_encoding, best_confidence
    
    def _quick_encoding_detection(self, data: bytes) -> Optional[str]:
        """快速编码检测"""
        # UTF-8 BOM检测
        if data.startswith(b'\xef\xbb\xbf'):
            return 'utf-8-sig'
        
        # UTF-16 BOM检测
        if data.startswith(b'\xff\xfe'):
            return 'utf-16-le'
        if data.startswith(b'\xfe\xff'):
            return 'utf-16-be'
        
        # UTF-32 BOM检测
        if data.startswith(b'\xff\xfe\x00\x00'):
            return 'utf-32-le'
        if data.startswith(b'\x00\x00\xfe\xff'):
            return 'utf-32-be'
        
        # ASCII检测
        try:
            data.decode('ascii')
            return 'ascii'
        except UnicodeDecodeError:
            pass
        
        # 中文编码特征检测
        if sys.platform == 'win32':
            gbk_score = self._calculate_gbk_score(data)
            if gbk_score > 0.7:
                return 'gbk'
        
        return None
    
    def _calculate_gbk_score(self, data: bytes) -> float:
        """计算GBK编码得分"""
        if len(data) < 10:
            return 0.0
        
        gbk_indicators = 0
        total_bytes = len(data)
        
        # 检测GBK双字节字符
        i = 0
        while i < len(data) - 1:
            byte1, byte2 = data[i], data[i + 1]
            
            # GBK第一字节范围：0x81-0xFE
            # GBK第二字节范围：0x40-0x7E, 0x80-0xFE
            if (0x81 <= byte1 <= 0xFE and 
                ((0x40 <= byte2 <= 0x7E) or (0x80 <= byte2 <= 0xFE))):
                gbk_indicators += 2
                i += 2
            else:
                i += 1
        
        return gbk_indicators / total_bytes
    
    def _calculate_encoding_confidence(self, data: bytes, encoding: str) -> float:
        """计算编码置信度"""
        try:
            decoded = data.decode(encoding, errors='strict')
            
            # 基础置信度
            confidence = 0.7
            
            # 根据字符分布调整置信度
            if encoding == 'utf-8':
                # UTF-8特征检测
                utf8_score = self._calculate_utf8_score(data)
                confidence = max(confidence, utf8_score)
            elif encoding in ['gbk', 'cp936']:
                # GBK特征检测
                gbk_score = self._calculate_gbk_score(data)
                confidence = max(confidence, gbk_score)
            
            # 检查是否包含常见的可打印字符
            printable_ratio = sum(1 for c in decoded if c.isprintable() or c.isspace()) / len(decoded)
            confidence *= printable_ratio
            
            return min(confidence, 0.95)
            
        except UnicodeDecodeError:
            return 0.0
    
    def _calculate_utf8_score(self, data: bytes) -> float:
        """计算UTF-8编码得分"""
        if len(data) < 10:
            return 0.7
        
        utf8_sequences = 0
        total_bytes = len(data)
        
        i = 0
        while i < len(data):
            byte = data[i]
            
            if byte < 0x80:
                # ASCII字符
                i += 1
            elif (byte & 0xE0) == 0xC0:
                # 2字节UTF-8序列
                if i + 1 < len(data) and (data[i + 1] & 0xC0) == 0x80:
                    utf8_sequences += 2
                    i += 2
                else:
                    i += 1
            elif (byte & 0xF0) == 0xE0:
                # 3字节UTF-8序列
                if (i + 2 < len(data) and 
                    (data[i + 1] & 0xC0) == 0x80 and 
                    (data[i + 2] & 0xC0) == 0x80):
                    utf8_sequences += 3
                    i += 3
                else:
                    i += 1
            elif (byte & 0xF8) == 0xF0:
                # 4字节UTF-8序列
                if (i + 3 < len(data) and 
                    (data[i + 1] & 0xC0) == 0x80 and 
                    (data[i + 2] & 0xC0) == 0x80 and 
                    (data[i + 3] & 0xC0) == 0x80):
                    utf8_sequences += 4
                    i += 4
                else:
                    i += 1
            else:
                i += 1
        
        return min(0.8 + (utf8_sequences / total_bytes) * 0.2, 0.95) 
   
    def _chardet_detection_with_confidence(self, data: bytes) -> Optional[Tuple[str, float]]:
        """使用chardet进行编码检测并返回置信度"""
        if not CHARDET_AVAILABLE:
            return None
        
        try:
            self._stats['chardet_calls'] += 1
            result = chardet.detect(data)
            
            if result and result['confidence'] > 0.6:
                encoding = result['encoding']
                confidence = result['confidence']
                
                # 标准化编码名称
                if encoding:
                    encoding = encoding.lower()
                    if encoding in ['gb2312', 'gb18030']:
                        encoding = 'gbk'
                    elif encoding.startswith('utf-8'):
                        encoding = 'utf-8'
                    elif encoding.startswith('iso-8859'):
                        encoding = 'latin1'
                
                return encoding, confidence
                
        except Exception as e:
            print(f"chardet检测出错: {str(e)}")
        
        return None
    
    def _test_encoding(self, data: bytes, encoding: str) -> bool:
        """测试编码是否有效"""
        try:
            data.decode(encoding, errors='strict')
            return True
        except (UnicodeDecodeError, LookupError):
            return False
    
    def _decode_with_encoding(self, raw_bytes: bytes, encoding: str) -> str:
        """使用指定编码解码数据"""
        try:
            return raw_bytes.decode(encoding, errors='strict')
        except UnicodeDecodeError:
            try:
                return raw_bytes.decode(encoding, errors='replace')
            except LookupError:
                return raw_bytes.decode(self._fallback_encoding, errors='replace')
    
    def _detect_line_pattern(self, line: str, line_index: int, all_lines: List[str]) -> Optional[LinePattern]:
        """检测单行的模式类型"""
        line_stripped = line.strip()
        if not line_stripped:
            return None
        
        # 检测ANSI颜色代码
        if self._ansi_color_pattern.search(line):
            ansi_count = len(self._ansi_color_pattern.findall(line))
            return LinePattern(
                pattern_type=LinePatternType.ANSI_COLORED,
                confidence=0.9,
                metadata={'ansi_sequences': ansi_count}
            )
        
        # 检测表格模式
        for i, pattern in enumerate(self._table_patterns):
            if pattern.match(line):
                confidence = 0.8 if i < 2 else 0.6  # 管道符和边框线置信度更高
                pattern_type = LinePatternType.TABLE_HEADER if '|' in line and line_index < 3 else LinePatternType.TABLE_ROW
                return LinePattern(
                    pattern_type=pattern_type,
                    confidence=confidence,
                    metadata={'table_pattern_index': i}
                )
        
        # 检测代码块（基于缩进）
        if line.startswith('    ') or line.startswith('\t'):
            indent_level = len(line) - len(line.lstrip())
            return LinePattern(
                pattern_type=LinePatternType.CODE_BLOCK,
                confidence=0.7,
                metadata={'indent_level': indent_level}
            )
        
        # 检测缩进文本
        if line.startswith('  ') and not line.startswith('    '):
            return LinePattern(
                pattern_type=LinePatternType.INDENTED_TEXT,
                confidence=0.6,
                metadata={'indent_spaces': len(line) - len(line.lstrip())}
            )
        
        # 检测Unicode重度使用
        unicode_count = sum(1 for char in line if self._is_unicode_char(char))
        if unicode_count > len(line) * 0.3:  # 超过30%的Unicode字符
            return LinePattern(
                pattern_type=LinePatternType.UNICODE_HEAVY,
                confidence=0.8,
                metadata={'unicode_ratio': unicode_count / len(line)}
            )
        
        return LinePattern(
            pattern_type=LinePatternType.NORMAL,
            confidence=0.5,
            metadata={}
        )
    
    def _is_unicode_char(self, char: str) -> bool:
        """检查字符是否为Unicode字符（非ASCII）"""
        char_code = ord(char)
        
        # ASCII范围
        if char_code < 128:
            return False
        
        # 检查是否在已知的Unicode范围内
        for start, end in self._unicode_ranges:
            if start <= char_code <= end:
                return True
        
        # 其他Unicode字符
        return char_code > 255
    
    def _analyze_special_characters(self, text: str) -> SpecialCharInfo:
        """分析文本中的特殊字符"""
        ansi_sequences = self._ansi_escape_pattern.findall(text)
        ansi_colors = self._ansi_color_pattern.findall(text)
        
        tab_count = text.count('\t')
        unicode_count = sum(1 for char in text if self._is_unicode_char(char))
        
        # 检测控制字符（除了常见的换行、制表符等）
        control_chars = sum(1 for char in text if ord(char) < 32 and char not in '\n\r\t')
        
        return SpecialCharInfo(
            has_ansi_sequences=len(ansi_sequences) > 0,
            has_tabs=tab_count > 0,
            has_unicode=unicode_count > 0,
            has_control_chars=control_chars > 0,
            ansi_color_count=len(ansi_colors),
            tab_count=tab_count,
            unicode_char_count=unicode_count
        )
    
    def _clean_control_characters(self, text: str) -> str:
        """清理控制字符（保留ANSI转义序列）"""
        # 保留常见的控制字符
        allowed_control_chars = {'\n', '\r', '\t', '\b', '\f'}
        
        cleaned_chars = []
        i = 0
        while i < len(text):
            char = text[i]
            
            # 检查ANSI转义序列
            if char == '\x1b' and i + 1 < len(text) and text[i + 1] == '[':
                # 找到ANSI序列的结束
                j = i + 2
                while j < len(text) and text[j] not in 'mK':
                    j += 1
                if j < len(text):
                    j += 1  # 包含结束字符
                
                # 保留整个ANSI序列
                cleaned_chars.append(text[i:j])
                i = j
                continue
            
            # 处理其他字符
            if ord(char) < 32:
                if char in allowed_control_chars:
                    cleaned_chars.append(char)
                # 其他控制字符被忽略
            else:
                cleaned_chars.append(char)
            
            i += 1
        
        return ''.join(cleaned_chars)
    
    def _normalize_unicode(self, text: str) -> str:
        """标准化Unicode字符"""
        try:
            import unicodedata
            # 使用NFC标准化
            return unicodedata.normalize('NFC', text)
        except ImportError:
            # 如果unicodedata不可用，直接返回原文本
            return text
    
    def _generate_cache_key(self, raw_bytes: bytes) -> int:
        """生成缓存键"""
        # 使用前128字节的哈希作为缓存键
        sample_size = min(128, len(raw_bytes))
        return hash(raw_bytes[:sample_size])
    
    def _get_cached_result(self, cache_key: int) -> Optional[DecodingResult]:
        """获取缓存的结果"""
        if cache_key in self._encoding_cache:
            result = self._encoding_cache[cache_key]
            # 移动到末尾（LRU）
            del self._encoding_cache[cache_key]
            self._encoding_cache[cache_key] = result
            
            self._stats['cache_hits'] += 1
            return result
        
        self._stats['cache_misses'] += 1
        return None
    
    def _cache_result(self, cache_key: int, result: DecodingResult) -> None:
        """缓存结果"""
        # 如果缓存已满，移除最旧的项
        if len(self._encoding_cache) >= self.cache_size:
            self._encoding_cache.popitem(last=False)
        
        # 创建结果副本（不包含处理时间）
        cached_result = DecodingResult(
            text=result.text,
            encoding=result.encoding,
            confidence=result.confidence,
            has_special_chars=result.has_special_chars,
            line_patterns=result.line_patterns,
            processing_time_ms=0.0  # 缓存结果不包含处理时间
        )
        
        self._encoding_cache[cache_key] = cached_result
    
    def _update_stats(self, encoding: str, special_char_info: SpecialCharInfo, line_patterns: List[LinePattern]) -> None:
        """更新统计信息"""
        # 更新编码分布
        if encoding not in self._stats['encoding_distribution']:
            self._stats['encoding_distribution'][encoding] = 0
        self._stats['encoding_distribution'][encoding] += 1
        
        # 更新特殊字符统计
        if special_char_info.has_ansi_sequences:
            self._stats['special_char_stats']['ansi_sequences'] += 1
        if special_char_info.has_tabs:
            self._stats['special_char_stats']['tabs'] += 1
        if special_char_info.has_unicode:
            self._stats['special_char_stats']['unicode_chars'] += 1
        if special_char_info.has_control_chars:
            self._stats['special_char_stats']['control_chars'] += 1
        
        # 更新模式分布
        for pattern in line_patterns:
            pattern_name = pattern.pattern_type.value
            if pattern_name not in self._stats['pattern_distribution']:
                self._stats['pattern_distribution'][pattern_name] = 0
            self._stats['pattern_distribution'][pattern_name] += 1
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        total_attempts = self._stats['cache_hits'] + self._stats['cache_misses']
        cache_hit_rate = (self._stats['cache_hits'] / total_attempts * 100) if total_attempts > 0 else 0
        
        return {
            'cache_size': len(self._encoding_cache),
            'cache_hit_rate': cache_hit_rate,
            'most_common_encoding': self._get_most_common_encoding(),
            'most_common_pattern': self._get_most_common_pattern(),
            **self._stats
        }
    
    def _get_most_common_encoding(self) -> Optional[str]:
        """获取最常用的编码"""
        if not self._stats['encoding_distribution']:
            return None
        return max(self._stats['encoding_distribution'].items(), key=lambda x: x[1])[0]
    
    def _get_most_common_pattern(self) -> Optional[str]:
        """获取最常用的模式"""
        if not self._stats['pattern_distribution']:
            return None
        return max(self._stats['pattern_distribution'].items(), key=lambda x: x[1])[0]
    
    def clear_cache(self) -> None:
        """清空缓存"""
        self._encoding_cache.clear()
        self._pattern_cache.clear()
    
    def set_primary_encodings(self, encodings: List[str]) -> None:
        """设置主要编码列表"""
        self._primary_encodings = encodings[:]
    
    def cleanup(self) -> None:
        """清理资源"""
        self.clear_cache()
        self._stats = {
            'total_detections': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'chardet_calls': 0,
            'pattern_detections': 0,
            'encoding_distribution': {},
            'pattern_distribution': {},
            'avg_detection_time': 0.0,
            'special_char_stats': {
                'ansi_sequences': 0,
                'tabs': 0,
                'unicode_chars': 0,
                'control_chars': 0
            }
        }